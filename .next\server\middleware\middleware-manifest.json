{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_f58e8655._.js", "server/edge/chunks/[root-of-the-server]__c6c91fdf._.js", "server/edge/chunks/edge-wrapper_c6630286.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "vIkrLhuFejbE7f2q9zDPJdA0q6VuXpH+EB9OFkD5UQc=", "__NEXT_PREVIEW_MODE_ID": "d6c7e63ae5713ad9b1fb579da1aaca7d", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "6fe041f5380ee6479489b9fa0b66588aea6c24196dc36a31c16f92211335ceaa", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "a02bd5a098dcd1c866a30e3888516f1caee85aa1d3f1529fed995870a0d41562"}}}, "instrumentation": null, "functions": {}}