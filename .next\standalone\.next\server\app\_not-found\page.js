(()=>{var e={};e.id=492,e.ids=[492],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7968:(e,r,t)=>{Promise.resolve().then(t.bind(t,85578))},9723:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>u,routeModule:()=>c,tree:()=>l});var s=t(65239),n=t(48088),o=t(88170),i=t.n(o),a=t(30893),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);t.d(r,d);let l={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,67393)),"C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,u=[],p={require:t,loadChunk:()=>Promise.resolve()},c=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},18375:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,86346,23)),Promise.resolve().then(t.t.bind(t,27924,23)),Promise.resolve().then(t.t.bind(t,35656,23)),Promise.resolve().then(t.t.bind(t,40099,23)),Promise.resolve().then(t.t.bind(t,38243,23)),Promise.resolve().then(t.t.bind(t,28827,23)),Promise.resolve().then(t.t.bind(t,62763,23)),Promise.resolve().then(t.t.bind(t,97173,23))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35247:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,16444,23)),Promise.resolve().then(t.t.bind(t,16042,23)),Promise.resolve().then(t.t.bind(t,88170,23)),Promise.resolve().then(t.t.bind(t,49477,23)),Promise.resolve().then(t.t.bind(t,29345,23)),Promise.resolve().then(t.t.bind(t,12089,23)),Promise.resolve().then(t.t.bind(t,46577,23)),Promise.resolve().then(t.t.bind(t,31307,23))},36328:(e,r,t)=>{"use strict";t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test2\\\\my-supabase-blog\\\\src\\\\components\\\\loading-spinner.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\loading-spinner.tsx","default")},52331:(e,r,t)=>{Promise.resolve().then(t.bind(t,96871))},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67393:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});var s=t(37413),n=t(36328);function o(){return(0,s.jsx)(n.default,{})}},78592:(e,r,t)=>{Promise.resolve().then(t.bind(t,36328))},83701:(e,r,t)=>{"use strict";t.d(r,{ThemeProvider:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\theme-provider.tsx","ThemeProvider")},85578:(e,r,t)=>{"use strict";t.d(r,{default:()=>n});var s=t(60687);function n(){return(0,s.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"h-24 w-24 rounded-full border-t-8 border-b-8 border-gray-200"}),(0,s.jsx)("div",{className:"absolute top-0 left-0 h-24 w-24 rounded-full border-t-8 border-b-8 border-blue-500 animate-spin"})]})})}t(43210)},92083:(e,r,t)=>{Promise.resolve().then(t.bind(t,83701))},94431:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>u,metadata:()=>l});var s=t(37413),n=t(22376),o=t.n(n),i=t(68726),a=t.n(i);t(61135);var d=t(83701);let l={title:"My Blog",description:"A modern blog built with Next.js and Supabase",icons:{icon:"/icon.png"}};function u({children:e}){return(0,s.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,s.jsxs)("body",{className:`${o().variable} ${a().variable} antialiased`,suppressHydrationWarning:!0,children:[(0,s.jsx)("meta",{name:"google-site-verification",content:"aY1T1sPS66KuuGAAe1zzVIZjS7YdM4MFbue__-3YBkI"}),(0,s.jsx)(d.ThemeProvider,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,suppressHydrationWarning:!0,children:e})]})})}},96871:(e,r,t)=>{"use strict";t.d(r,{ThemeProvider:()=>o});var s=t(60687);t(43210);var n=t(10218);function o({children:e,...r}){return(0,s.jsx)(n.N,{...r,children:e})}}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,98],()=>t(9723));module.exports=s})();