{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/app/admin/loading.tsx"], "sourcesContent": ["import LoadingSpinner from '@/components/loading-spinner';\r\n\r\nexport default function AdminLoading() {\r\n  return <LoadingSpinner />;\r\n}"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,qBAAO,8OAAC,wIAAA,CAAA,UAAc;;;;;AACxB", "debugId": null}}]}