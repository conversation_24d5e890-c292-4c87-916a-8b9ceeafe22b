{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/loading-spinner.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\n\r\nexport default function LoadingSpinner() {\r\n  return (\r\n    <div className=\"flex items-center justify-center min-h-screen\">\r\n      <div className=\"relative\">\r\n        <div className=\"h-24 w-24 rounded-full border-t-8 border-b-8 border-gray-200\"></div>\r\n        <div className=\"absolute top-0 left-0 h-24 w-24 rounded-full border-t-8 border-b-8 border-blue-500 animate-spin\">\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAAA;;AAIe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;;AAKvB", "debugId": null}}]}