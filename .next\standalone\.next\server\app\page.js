(()=>{var e={};e.id=974,e.ids=[974],e.modules={363:(e,r,t)=>{"use strict";t.d(r,{A:()=>o});let o=(0,t(62688).A)("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])},2507:(e,r,t)=>{"use strict";t.d(r,{U:()=>n});var o=t(9866),s=t(44999);async function n(){let e=await (0,s.UL)();return(0,o.createServerClient)("https://sqoixvgmroejgaebxyeq.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNxb2l4dmdtcm9lamdhZWJ4eWVxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExNjU0ODksImV4cCI6MjA2Njc0MTQ4OX0.SFkF9v0Y-lrJuQHiSf5HLTLuwgOXRf0ERQKnfQNfLsU",{cookies:{getAll:()=>e.getAll(),setAll(r){try{r.forEach(({name:r,value:t,options:o})=>e.set(r,t,o))}catch{}}}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4329:(e,r,t)=>{"use strict";t.d(r,{HeroSkeleton:()=>l,PostGridSkeleton:()=>i});var o=t(60687),s=t(6360);function n({className:e}){return(0,o.jsx)("div",{className:(0,s.cn)("animate-pulse rounded-md bg-muted",e)})}function a({className:e}){return(0,o.jsx)("div",{className:(0,s.cn)("bg-card rounded-xl shadow-sm border border-border p-6 lg:p-8",e),children:(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsx)(n,{className:"h-7 w-3/4"}),(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsx)(n,{className:"h-4 w-full"}),(0,o.jsx)(n,{className:"h-4 w-full"}),(0,o.jsx)(n,{className:"h-4 w-2/3"})]}),(0,o.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 pt-2",children:[(0,o.jsxs)("div",{className:"flex items-center gap-2",children:[(0,o.jsx)(n,{className:"h-4 w-4"}),(0,o.jsx)(n,{className:"h-4 w-32"})]}),(0,o.jsx)(n,{className:"h-4 w-20"})]})]})})}function i({count:e=6}){return(0,o.jsx)("div",{className:"grid gap-6 lg:gap-8 md:grid-cols-2 lg:grid-cols-3",children:Array.from({length:e}).map((e,r)=>(0,o.jsx)(a,{},r))})}function l(){return(0,o.jsxs)("div",{className:"text-center py-16 lg:py-24 space-y-6",children:[(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsx)(n,{className:"h-12 w-2/3 mx-auto"}),(0,o.jsx)(n,{className:"h-6 w-1/2 mx-auto"})]}),(0,o.jsxs)("div",{className:"space-y-3",children:[(0,o.jsx)(n,{className:"h-4 w-3/4 mx-auto"}),(0,o.jsx)(n,{className:"h-4 w-2/3 mx-auto"})]}),(0,o.jsx)(n,{className:"h-12 w-40 mx-auto"})]})}},6360:(e,r,t)=>{"use strict";t.d(r,{_C:()=>em,cn:()=>ec,gQ:()=>ep,Yq:()=>ex,g7:()=>eh,CI:()=>eu});let o=e=>{let r=i(e),{conflictingClassGroups:t,conflictingClassGroupModifiers:o}=e;return{getClassGroupId:e=>{let t=e.split("-");return""===t[0]&&1!==t.length&&t.shift(),s(t,r)||a(e)},getConflictingClassGroupIds:(e,r)=>{let s=t[e]||[];return r&&o[e]?[...s,...o[e]]:s}}},s=(e,r)=>{if(0===e.length)return r.classGroupId;let t=e[0],o=r.nextPart.get(t),n=o?s(e.slice(1),o):void 0;if(n)return n;if(0===r.validators.length)return;let a=e.join("-");return r.validators.find(({validator:e})=>e(a))?.classGroupId},n=/^\[(.+)\]$/,a=e=>{if(n.test(e)){let r=n.exec(e)[1],t=r?.substring(0,r.indexOf(":"));if(t)return"arbitrary.."+t}},i=e=>{let{theme:r,classGroups:t}=e,o={nextPart:new Map,validators:[]};for(let e in t)l(t[e],o,e,r);return o},l=(e,r,t,o)=>{e.forEach(e=>{if("string"==typeof e){(""===e?r:d(r,e)).classGroupId=t;return}if("function"==typeof e)return c(e)?void l(e(o),r,t,o):void r.validators.push({validator:e,classGroupId:t});Object.entries(e).forEach(([e,s])=>{l(s,d(r,e),t,o)})})},d=(e,r)=>{let t=e;return r.split("-").forEach(e=>{t.nextPart.has(e)||t.nextPart.set(e,{nextPart:new Map,validators:[]}),t=t.nextPart.get(e)}),t},c=e=>e.isThemeGetter,m=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let r=0,t=new Map,o=new Map,s=(s,n)=>{t.set(s,n),++r>e&&(r=0,o=t,t=new Map)};return{get(e){let r=t.get(e);return void 0!==r?r:void 0!==(r=o.get(e))?(s(e,r),r):void 0},set(e,r){t.has(e)?t.set(e,r):s(e,r)}}},p=e=>{let{prefix:r,experimentalParseClassName:t}=e,o=e=>{let r,t=[],o=0,s=0,n=0;for(let a=0;a<e.length;a++){let i=e[a];if(0===o&&0===s){if(":"===i){t.push(e.slice(n,a)),n=a+1;continue}if("/"===i){r=a;continue}}"["===i?o++:"]"===i?o--:"("===i?s++:")"===i&&s--}let a=0===t.length?e:e.substring(n),i=u(a);return{modifiers:t,hasImportantModifier:i!==a,baseClassName:i,maybePostfixModifierPosition:r&&r>n?r-n:void 0}};if(r){let e=r+":",t=o;o=r=>r.startsWith(e)?t(r.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:r,maybePostfixModifierPosition:void 0}}if(t){let e=o;o=r=>t({className:r,parseClassName:e})}return o},u=e=>e.endsWith("!")?e.substring(0,e.length-1):e.startsWith("!")?e.substring(1):e,h=e=>{let r=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let t=[],o=[];return e.forEach(e=>{"["===e[0]||r[e]?(t.push(...o.sort(),e),o=[]):o.push(e)}),t.push(...o.sort()),t}},x=e=>({cache:m(e.cacheSize),parseClassName:p(e),sortModifiers:h(e),...o(e)}),g=/\s+/,f=(e,r)=>{let{parseClassName:t,getClassGroupId:o,getConflictingClassGroupIds:s,sortModifiers:n}=r,a=[],i=e.trim().split(g),l="";for(let e=i.length-1;e>=0;e-=1){let r=i[e],{isExternal:d,modifiers:c,hasImportantModifier:m,baseClassName:p,maybePostfixModifierPosition:u}=t(r);if(d){l=r+(l.length>0?" "+l:l);continue}let h=!!u,x=o(h?p.substring(0,u):p);if(!x){if(!h||!(x=o(p))){l=r+(l.length>0?" "+l:l);continue}h=!1}let g=n(c).join(":"),f=m?g+"!":g,b=f+x;if(a.includes(b))continue;a.push(b);let v=s(x,h);for(let e=0;e<v.length;++e){let r=v[e];a.push(f+r)}l=r+(l.length>0?" "+l:l)}return l};function b(){let e,r,t=0,o="";for(;t<arguments.length;)(e=arguments[t++])&&(r=v(e))&&(o&&(o+=" "),o+=r);return o}let v=e=>{let r;if("string"==typeof e)return e;let t="";for(let o=0;o<e.length;o++)e[o]&&(r=v(e[o]))&&(t&&(t+=" "),t+=r);return t},k=e=>{let r=r=>r[e]||[];return r.isThemeGetter=!0,r},y=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,w=/^\((?:(\w[\w-]*):)?(.+)\)$/i,j=/^\d+\/\d+$/,N=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,C=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,P=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,z=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,S=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,M=e=>j.test(e),L=e=>!!e&&!Number.isNaN(Number(e)),W=e=>!!e&&Number.isInteger(Number(e)),I=e=>e.endsWith("%")&&L(e.slice(0,-1)),A=e=>N.test(e),H=()=>!0,q=e=>C.test(e)&&!P.test(e),E=()=>!1,U=e=>z.test(e),_=e=>S.test(e),B=e=>!G(e)&&!O(e),D=e=>ee(e,es,E),G=e=>y.test(e),T=e=>ee(e,en,q),$=e=>ee(e,ea,L),R=e=>ee(e,et,E),V=e=>ee(e,eo,_),F=e=>ee(e,el,U),O=e=>w.test(e),Y=e=>er(e,en),J=e=>er(e,ei),Z=e=>er(e,et),Q=e=>er(e,es),X=e=>er(e,eo),K=e=>er(e,el,!0),ee=(e,r,t)=>{let o=y.exec(e);return!!o&&(o[1]?r(o[1]):t(o[2]))},er=(e,r,t=!1)=>{let o=w.exec(e);return!!o&&(o[1]?r(o[1]):t)},et=e=>"position"===e||"percentage"===e,eo=e=>"image"===e||"url"===e,es=e=>"length"===e||"size"===e||"bg-size"===e,en=e=>"length"===e,ea=e=>"number"===e,ei=e=>"family-name"===e,el=e=>"shadow"===e;Symbol.toStringTag;let ed=function(e,...r){let t,o,s,n=function(i){return o=(t=x(r.reduce((e,r)=>r(e),e()))).cache.get,s=t.cache.set,n=a,a(i)};function a(e){let r=o(e);if(r)return r;let n=f(e,t);return s(e,n),n}return function(){return n(b.apply(null,arguments))}}(()=>{let e=k("color"),r=k("font"),t=k("text"),o=k("font-weight"),s=k("tracking"),n=k("leading"),a=k("breakpoint"),i=k("container"),l=k("spacing"),d=k("radius"),c=k("shadow"),m=k("inset-shadow"),p=k("text-shadow"),u=k("drop-shadow"),h=k("blur"),x=k("perspective"),g=k("aspect"),f=k("ease"),b=k("animate"),v=()=>["auto","avoid","all","avoid-page","page","left","right","column"],y=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],w=()=>[...y(),O,G],j=()=>["auto","hidden","clip","visible","scroll"],N=()=>["auto","contain","none"],C=()=>[O,G,l],P=()=>[M,"full","auto",...C()],z=()=>[W,"none","subgrid",O,G],S=()=>["auto",{span:["full",W,O,G]},W,O,G],q=()=>[W,"auto",O,G],E=()=>["auto","min","max","fr",O,G],U=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],_=()=>["start","end","center","stretch","center-safe","end-safe"],ee=()=>["auto",...C()],er=()=>[M,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...C()],et=()=>[e,O,G],eo=()=>[...y(),Z,R,{position:[O,G]}],es=()=>["no-repeat",{repeat:["","x","y","space","round"]}],en=()=>["auto","cover","contain",Q,D,{size:[O,G]}],ea=()=>[I,Y,T],ei=()=>["","none","full",d,O,G],el=()=>["",L,Y,T],ed=()=>["solid","dashed","dotted","double"],ec=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],em=()=>[L,I,Z,R],ep=()=>["","none",h,O,G],eu=()=>["none",L,O,G],eh=()=>["none",L,O,G],ex=()=>[L,O,G],eg=()=>[M,"full",...C()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[A],breakpoint:[A],color:[H],container:[A],"drop-shadow":[A],ease:["in","out","in-out"],font:[B],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[A],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[A],shadow:[A],spacing:["px",L],text:[A],"text-shadow":[A],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",M,G,O,g]}],container:["container"],columns:[{columns:[L,G,O,i]}],"break-after":[{"break-after":v()}],"break-before":[{"break-before":v()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:w()}],overflow:[{overflow:j()}],"overflow-x":[{"overflow-x":j()}],"overflow-y":[{"overflow-y":j()}],overscroll:[{overscroll:N()}],"overscroll-x":[{"overscroll-x":N()}],"overscroll-y":[{"overscroll-y":N()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:P()}],"inset-x":[{"inset-x":P()}],"inset-y":[{"inset-y":P()}],start:[{start:P()}],end:[{end:P()}],top:[{top:P()}],right:[{right:P()}],bottom:[{bottom:P()}],left:[{left:P()}],visibility:["visible","invisible","collapse"],z:[{z:[W,"auto",O,G]}],basis:[{basis:[M,"full","auto",i,...C()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[L,M,"auto","initial","none",G]}],grow:[{grow:["",L,O,G]}],shrink:[{shrink:["",L,O,G]}],order:[{order:[W,"first","last","none",O,G]}],"grid-cols":[{"grid-cols":z()}],"col-start-end":[{col:S()}],"col-start":[{"col-start":q()}],"col-end":[{"col-end":q()}],"grid-rows":[{"grid-rows":z()}],"row-start-end":[{row:S()}],"row-start":[{"row-start":q()}],"row-end":[{"row-end":q()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":E()}],"auto-rows":[{"auto-rows":E()}],gap:[{gap:C()}],"gap-x":[{"gap-x":C()}],"gap-y":[{"gap-y":C()}],"justify-content":[{justify:[...U(),"normal"]}],"justify-items":[{"justify-items":[..._(),"normal"]}],"justify-self":[{"justify-self":["auto",..._()]}],"align-content":[{content:["normal",...U()]}],"align-items":[{items:[..._(),{baseline:["","last"]}]}],"align-self":[{self:["auto",..._(),{baseline:["","last"]}]}],"place-content":[{"place-content":U()}],"place-items":[{"place-items":[..._(),"baseline"]}],"place-self":[{"place-self":["auto",..._()]}],p:[{p:C()}],px:[{px:C()}],py:[{py:C()}],ps:[{ps:C()}],pe:[{pe:C()}],pt:[{pt:C()}],pr:[{pr:C()}],pb:[{pb:C()}],pl:[{pl:C()}],m:[{m:ee()}],mx:[{mx:ee()}],my:[{my:ee()}],ms:[{ms:ee()}],me:[{me:ee()}],mt:[{mt:ee()}],mr:[{mr:ee()}],mb:[{mb:ee()}],ml:[{ml:ee()}],"space-x":[{"space-x":C()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":C()}],"space-y-reverse":["space-y-reverse"],size:[{size:er()}],w:[{w:[i,"screen",...er()]}],"min-w":[{"min-w":[i,"screen","none",...er()]}],"max-w":[{"max-w":[i,"screen","none","prose",{screen:[a]},...er()]}],h:[{h:["screen","lh",...er()]}],"min-h":[{"min-h":["screen","lh","none",...er()]}],"max-h":[{"max-h":["screen","lh",...er()]}],"font-size":[{text:["base",t,Y,T]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[o,O,$]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",I,G]}],"font-family":[{font:[J,G,r]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[s,O,G]}],"line-clamp":[{"line-clamp":[L,"none",O,$]}],leading:[{leading:[n,...C()]}],"list-image":[{"list-image":["none",O,G]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",O,G]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:et()}],"text-color":[{text:et()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ed(),"wavy"]}],"text-decoration-thickness":[{decoration:[L,"from-font","auto",O,T]}],"text-decoration-color":[{decoration:et()}],"underline-offset":[{"underline-offset":[L,"auto",O,G]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:C()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",O,G]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",O,G]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:eo()}],"bg-repeat":[{bg:es()}],"bg-size":[{bg:en()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},W,O,G],radial:["",O,G],conic:[W,O,G]},X,V]}],"bg-color":[{bg:et()}],"gradient-from-pos":[{from:ea()}],"gradient-via-pos":[{via:ea()}],"gradient-to-pos":[{to:ea()}],"gradient-from":[{from:et()}],"gradient-via":[{via:et()}],"gradient-to":[{to:et()}],rounded:[{rounded:ei()}],"rounded-s":[{"rounded-s":ei()}],"rounded-e":[{"rounded-e":ei()}],"rounded-t":[{"rounded-t":ei()}],"rounded-r":[{"rounded-r":ei()}],"rounded-b":[{"rounded-b":ei()}],"rounded-l":[{"rounded-l":ei()}],"rounded-ss":[{"rounded-ss":ei()}],"rounded-se":[{"rounded-se":ei()}],"rounded-ee":[{"rounded-ee":ei()}],"rounded-es":[{"rounded-es":ei()}],"rounded-tl":[{"rounded-tl":ei()}],"rounded-tr":[{"rounded-tr":ei()}],"rounded-br":[{"rounded-br":ei()}],"rounded-bl":[{"rounded-bl":ei()}],"border-w":[{border:el()}],"border-w-x":[{"border-x":el()}],"border-w-y":[{"border-y":el()}],"border-w-s":[{"border-s":el()}],"border-w-e":[{"border-e":el()}],"border-w-t":[{"border-t":el()}],"border-w-r":[{"border-r":el()}],"border-w-b":[{"border-b":el()}],"border-w-l":[{"border-l":el()}],"divide-x":[{"divide-x":el()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":el()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...ed(),"hidden","none"]}],"divide-style":[{divide:[...ed(),"hidden","none"]}],"border-color":[{border:et()}],"border-color-x":[{"border-x":et()}],"border-color-y":[{"border-y":et()}],"border-color-s":[{"border-s":et()}],"border-color-e":[{"border-e":et()}],"border-color-t":[{"border-t":et()}],"border-color-r":[{"border-r":et()}],"border-color-b":[{"border-b":et()}],"border-color-l":[{"border-l":et()}],"divide-color":[{divide:et()}],"outline-style":[{outline:[...ed(),"none","hidden"]}],"outline-offset":[{"outline-offset":[L,O,G]}],"outline-w":[{outline:["",L,Y,T]}],"outline-color":[{outline:et()}],shadow:[{shadow:["","none",c,K,F]}],"shadow-color":[{shadow:et()}],"inset-shadow":[{"inset-shadow":["none",m,K,F]}],"inset-shadow-color":[{"inset-shadow":et()}],"ring-w":[{ring:el()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:et()}],"ring-offset-w":[{"ring-offset":[L,T]}],"ring-offset-color":[{"ring-offset":et()}],"inset-ring-w":[{"inset-ring":el()}],"inset-ring-color":[{"inset-ring":et()}],"text-shadow":[{"text-shadow":["none",p,K,F]}],"text-shadow-color":[{"text-shadow":et()}],opacity:[{opacity:[L,O,G]}],"mix-blend":[{"mix-blend":[...ec(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ec()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[L]}],"mask-image-linear-from-pos":[{"mask-linear-from":em()}],"mask-image-linear-to-pos":[{"mask-linear-to":em()}],"mask-image-linear-from-color":[{"mask-linear-from":et()}],"mask-image-linear-to-color":[{"mask-linear-to":et()}],"mask-image-t-from-pos":[{"mask-t-from":em()}],"mask-image-t-to-pos":[{"mask-t-to":em()}],"mask-image-t-from-color":[{"mask-t-from":et()}],"mask-image-t-to-color":[{"mask-t-to":et()}],"mask-image-r-from-pos":[{"mask-r-from":em()}],"mask-image-r-to-pos":[{"mask-r-to":em()}],"mask-image-r-from-color":[{"mask-r-from":et()}],"mask-image-r-to-color":[{"mask-r-to":et()}],"mask-image-b-from-pos":[{"mask-b-from":em()}],"mask-image-b-to-pos":[{"mask-b-to":em()}],"mask-image-b-from-color":[{"mask-b-from":et()}],"mask-image-b-to-color":[{"mask-b-to":et()}],"mask-image-l-from-pos":[{"mask-l-from":em()}],"mask-image-l-to-pos":[{"mask-l-to":em()}],"mask-image-l-from-color":[{"mask-l-from":et()}],"mask-image-l-to-color":[{"mask-l-to":et()}],"mask-image-x-from-pos":[{"mask-x-from":em()}],"mask-image-x-to-pos":[{"mask-x-to":em()}],"mask-image-x-from-color":[{"mask-x-from":et()}],"mask-image-x-to-color":[{"mask-x-to":et()}],"mask-image-y-from-pos":[{"mask-y-from":em()}],"mask-image-y-to-pos":[{"mask-y-to":em()}],"mask-image-y-from-color":[{"mask-y-from":et()}],"mask-image-y-to-color":[{"mask-y-to":et()}],"mask-image-radial":[{"mask-radial":[O,G]}],"mask-image-radial-from-pos":[{"mask-radial-from":em()}],"mask-image-radial-to-pos":[{"mask-radial-to":em()}],"mask-image-radial-from-color":[{"mask-radial-from":et()}],"mask-image-radial-to-color":[{"mask-radial-to":et()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":y()}],"mask-image-conic-pos":[{"mask-conic":[L]}],"mask-image-conic-from-pos":[{"mask-conic-from":em()}],"mask-image-conic-to-pos":[{"mask-conic-to":em()}],"mask-image-conic-from-color":[{"mask-conic-from":et()}],"mask-image-conic-to-color":[{"mask-conic-to":et()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:eo()}],"mask-repeat":[{mask:es()}],"mask-size":[{mask:en()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",O,G]}],filter:[{filter:["","none",O,G]}],blur:[{blur:ep()}],brightness:[{brightness:[L,O,G]}],contrast:[{contrast:[L,O,G]}],"drop-shadow":[{"drop-shadow":["","none",u,K,F]}],"drop-shadow-color":[{"drop-shadow":et()}],grayscale:[{grayscale:["",L,O,G]}],"hue-rotate":[{"hue-rotate":[L,O,G]}],invert:[{invert:["",L,O,G]}],saturate:[{saturate:[L,O,G]}],sepia:[{sepia:["",L,O,G]}],"backdrop-filter":[{"backdrop-filter":["","none",O,G]}],"backdrop-blur":[{"backdrop-blur":ep()}],"backdrop-brightness":[{"backdrop-brightness":[L,O,G]}],"backdrop-contrast":[{"backdrop-contrast":[L,O,G]}],"backdrop-grayscale":[{"backdrop-grayscale":["",L,O,G]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[L,O,G]}],"backdrop-invert":[{"backdrop-invert":["",L,O,G]}],"backdrop-opacity":[{"backdrop-opacity":[L,O,G]}],"backdrop-saturate":[{"backdrop-saturate":[L,O,G]}],"backdrop-sepia":[{"backdrop-sepia":["",L,O,G]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":C()}],"border-spacing-x":[{"border-spacing-x":C()}],"border-spacing-y":[{"border-spacing-y":C()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",O,G]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[L,"initial",O,G]}],ease:[{ease:["linear","initial",f,O,G]}],delay:[{delay:[L,O,G]}],animate:[{animate:["none",b,O,G]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[x,O,G]}],"perspective-origin":[{"perspective-origin":w()}],rotate:[{rotate:eu()}],"rotate-x":[{"rotate-x":eu()}],"rotate-y":[{"rotate-y":eu()}],"rotate-z":[{"rotate-z":eu()}],scale:[{scale:eh()}],"scale-x":[{"scale-x":eh()}],"scale-y":[{"scale-y":eh()}],"scale-z":[{"scale-z":eh()}],"scale-3d":["scale-3d"],skew:[{skew:ex()}],"skew-x":[{"skew-x":ex()}],"skew-y":[{"skew-y":ex()}],transform:[{transform:[O,G,"","none","gpu","cpu"]}],"transform-origin":[{origin:w()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:eg()}],"translate-x":[{"translate-x":eg()}],"translate-y":[{"translate-y":eg()}],"translate-z":[{"translate-z":eg()}],"translate-none":["translate-none"],accent:[{accent:et()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:et()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",O,G]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":C()}],"scroll-mx":[{"scroll-mx":C()}],"scroll-my":[{"scroll-my":C()}],"scroll-ms":[{"scroll-ms":C()}],"scroll-me":[{"scroll-me":C()}],"scroll-mt":[{"scroll-mt":C()}],"scroll-mr":[{"scroll-mr":C()}],"scroll-mb":[{"scroll-mb":C()}],"scroll-ml":[{"scroll-ml":C()}],"scroll-p":[{"scroll-p":C()}],"scroll-px":[{"scroll-px":C()}],"scroll-py":[{"scroll-py":C()}],"scroll-ps":[{"scroll-ps":C()}],"scroll-pe":[{"scroll-pe":C()}],"scroll-pt":[{"scroll-pt":C()}],"scroll-pr":[{"scroll-pr":C()}],"scroll-pb":[{"scroll-pb":C()}],"scroll-pl":[{"scroll-pl":C()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",O,G]}],fill:[{fill:["none",...et()]}],"stroke-w":[{stroke:[L,Y,T,$]}],stroke:[{stroke:["none",...et()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}});function ec(...e){return ed(function(){for(var e,r,t=0,o="",s=arguments.length;t<s;t++)(e=arguments[t])&&(r=function e(r){var t,o,s="";if("string"==typeof r||"number"==typeof r)s+=r;else if("object"==typeof r)if(Array.isArray(r)){var n=r.length;for(t=0;t<n;t++)r[t]&&(o=e(r[t]))&&(s&&(s+=" "),s+=o)}else for(o in r)r[o]&&(s&&(s+=" "),s+=o);return s}(e))&&(o&&(o+=" "),o+=r);return o}(e))}function em(e,r=200){return Math.max(1,Math.ceil(e.replace(/```[\s\S]*?```/g,"").replace(/`[^`]*`/g,"").replace(/#{1,6}\s/g,"").replace(/\*\*([^*]+)\*\*/g,"$1").replace(/\*([^*]+)\*/g,"$1").replace(/\[([^\]]+)\]\([^)]+\)/g,"$1").replace(/<[^>]*>/g,"").replace(/\s+/g," ").trim().split(" ").filter(e=>e.length>0).length/r))}function ep(e,r=200){let t=e.replace(/```[\s\S]*?```/g,"").replace(/`[^`]*`/g,"").replace(/#{1,6}\s/g,"").replace(/\*\*([^*]+)\*\*/g,"$1").replace(/\*([^*]+)\*/g,"$1").replace(/\[([^\]]+)\]\([^)]+\)/g,"$1").replace(/<[^>]*>/g,"").replace(/\s+/g," ").trim();if(t.length<=r)return t;let o=t.substring(0,r),s=o.lastIndexOf(" ");return s>0?o.substring(0,s)+"...":o+"..."}function eu(e,r){if(!r.trim())return e;let t=r.toLowerCase().trim();return e.filter(e=>{let r=e.title.toLowerCase().includes(t),o=e.content.toLowerCase().includes(t);return r||o})}function eh(e,r,t){let o=e.length,s=Math.ceil(o/t),n=Math.max(1,Math.min(r,s)),a=(n-1)*t;return{items:e.slice(a,a+t),totalItems:o,totalPages:s,currentPage:n,hasNextPage:n<s,hasPreviousPage:n>1}}function ex(e,r={year:"numeric",month:"long",day:"numeric"}){return new Date(e).toLocaleDateString("en-US",r)}},6561:(e,r,t)=>{Promise.resolve().then(t.bind(t,94340)),Promise.resolve().then(t.bind(t,98575))},7968:(e,r,t)=>{Promise.resolve().then(t.bind(t,85578))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},12909:(e,r,t)=>{"use strict";t.d(r,{HW:()=>s,qc:()=>n});var o=t(2507);async function s(){let e=await (0,o.U)(),{data:{user:r}}=await e.auth.getUser();return r}async function n(e){if(!e)return!1;let r=await (0,o.U)(),{data:t}=await r.from("profiles").select("is_admin").eq("id",e).single();if(t?.is_admin)return!0;let{data:{user:s}}=await r.auth.getUser(),n=process.env.ADMIN_EMAIL;return s?.email===n}},18375:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,86346,23)),Promise.resolve().then(t.t.bind(t,27924,23)),Promise.resolve().then(t.t.bind(t,35656,23)),Promise.resolve().then(t.t.bind(t,40099,23)),Promise.resolve().then(t.t.bind(t,38243,23)),Promise.resolve().then(t.t.bind(t,28827,23)),Promise.resolve().then(t.t.bind(t,62763,23)),Promise.resolve().then(t.t.bind(t,97173,23))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20077:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>d});var o=t(65239),s=t(48088),n=t(88170),a=t.n(n),i=t(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);t.d(r,l);let d={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,21204)),"C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,67393)),"C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},p=new o.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},21134:(e,r,t)=>{"use strict";t.d(r,{A:()=>o});let o=(0,t(62688).A)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},21204:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d});var o=t(37413),s=t(61120),n=t(2507),a=t(12909),i=t(94340),l=t(98575);async function d(){let e=await (0,n.U)(),{data:r,error:t}=await e.from("posts").select("*").order("created_at",{ascending:!1});if(t)return console.error("Error fetching posts:",t),(0,o.jsx)("div",{children:"Error loading posts."});let d=await (0,a.HW)(),c=!!d&&await (0,a.qc)(d.id);return(0,o.jsx)("div",{className:"min-h-screen bg-background",children:(0,o.jsx)(s.Suspense,{fallback:(0,o.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,o.jsx)(l.HeroSkeleton,{}),(0,o.jsx)(l.PostGridSkeleton,{})]}),children:(0,o.jsx)(i.HomePageClient,{initialPosts:r??[],user:d??null,userIsAdmin:c??!1})})})}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},35247:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,16444,23)),Promise.resolve().then(t.t.bind(t,16042,23)),Promise.resolve().then(t.t.bind(t,88170,23)),Promise.resolve().then(t.t.bind(t,49477,23)),Promise.resolve().then(t.t.bind(t,29345,23)),Promise.resolve().then(t.t.bind(t,12089,23)),Promise.resolve().then(t.t.bind(t,46577,23)),Promise.resolve().then(t.t.bind(t,31307,23))},36328:(e,r,t)=>{"use strict";t.d(r,{default:()=>o});let o=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test2\\\\my-supabase-blog\\\\src\\\\components\\\\loading-spinner.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\loading-spinner.tsx","default")},39727:()=>{},43009:(e,r,t)=>{Promise.resolve().then(t.bind(t,51584)),Promise.resolve().then(t.bind(t,4329))},43984:(e,r,t)=>{"use strict";t.d(r,{ThemeToggle:()=>l});var o=t(60687),s=t(43210),n=t(21134),a=t(363),i=t(10218);function l(){let{theme:e,setTheme:r}=(0,i.D)(),[t,l]=s.useState(!1);return(s.useEffect(()=>{l(!0)},[]),t)?(0,o.jsxs)("button",{onClick:()=>r("light"===e?"dark":"light"),className:"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 w-10",children:["light"===e?(0,o.jsx)(a.A,{className:"h-[1.2rem] w-[1.2rem]"}):(0,o.jsx)(n.A,{className:"h-[1.2rem] w-[1.2rem]"}),(0,o.jsx)("span",{className:"sr-only",children:"切換主題"})]}):(0,o.jsxs)("button",{className:"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 w-10",children:[(0,o.jsx)(n.A,{className:"h-[1.2rem] w-[1.2rem]"}),(0,o.jsx)("span",{className:"sr-only",children:"切換主題"})]})}},47990:()=>{},51584:(e,r,t)=>{"use strict";t.d(r,{HomePageClient:()=>x});var o=t(60687),s=t(43210),n=t(85814),a=t.n(n),i=t(6360);function l({post:e,featured:r=!1,className:t,showExcerpt:s=!0,excerptLength:n=200}){let l=(0,i._C)(e.content),d=s?(0,i.gQ)(e.content,n):"",c=new Date(e.created_at)>new Date(Date.now()-864e5);return r?(0,o.jsxs)("article",{className:(0,i.cn)("group relative overflow-hidden","bg-gradient-to-br from-primary/5 via-primary/3 to-background","rounded-2xl border border-primary/20 shadow-lg","hover:shadow-xl hover:border-primary/30","transition-all duration-300","p-6 lg:p-8",t),children:[(0,o.jsx)("div",{className:"absolute top-4 right-4",suppressHydrationWarning:!0,children:(0,o.jsx)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-primary text-primary-foreground shadow-sm",children:(0,o.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})})})}),(0,o.jsxs)("div",{className:"space-y-4",suppressHydrationWarning:!0,children:[(0,o.jsxs)("div",{suppressHydrationWarning:!0,children:[(0,o.jsx)("h2",{className:"text-2xl lg:text-3xl font-bold text-foreground mb-3 group-hover:text-primary transition-colors leading-tight",children:(0,o.jsx)(a(),{href:`/posts/${e.id}`,className:"block",children:e.title})}),s&&(0,o.jsx)("p",{className:"text-muted-foreground leading-relaxed text-base lg:text-lg",children:d})]}),(0,o.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 pt-2",suppressHydrationWarning:!0,children:[(0,o.jsxs)("div",{className:"flex items-center gap-4 text-sm text-muted-foreground",suppressHydrationWarning:!0,children:[(0,o.jsxs)("time",{className:"flex items-center gap-1",children:[(0,o.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})}),(0,i.Yq)(e.created_at)]}),(0,o.jsxs)("span",{className:"flex items-center gap-1",children:[(0,o.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})}),l," min read"]})]}),(0,o.jsxs)(a(),{href:`/posts/${e.id}`,className:"inline-flex items-center text-primary hover:text-primary/80 font-medium transition-all duration-200 group/link text-sm",children:["Read full article",(0,o.jsx)("svg",{className:"w-4 h-4 ml-1 group-hover/link:translate-x-1 transition-transform",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]})]})]})]}):(0,o.jsxs)("article",{className:(0,i.cn)("group relative overflow-hidden","bg-card rounded-xl shadow-sm border border-border","hover:shadow-xl hover:border-primary/30 hover:-translate-y-1","transition-all duration-300 ease-out","p-6",t),children:[c&&(0,o.jsx)("div",{className:"absolute top-6 right-6 z-10",suppressHydrationWarning:!0,children:(0,o.jsxs)("span",{className:"inline-flex items-center px-3.5 py-1.5 rounded-full text-xs font-bold bg-gradient-to-br from-green-400 to-emerald-600 text-white shadow-xl transform hover:scale-105 transition-all duration-300 ease-out",children:[(0,o.jsx)("svg",{className:"w-3 h-3 mr-1",fill:"currentColor",viewBox:"0 0 20 20",children:(0,o.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),"New"]})}),(0,o.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl",suppressHydrationWarning:!0}),(0,o.jsxs)("div",{className:"relative space-y-4",suppressHydrationWarning:!0,children:[(0,o.jsxs)("div",{suppressHydrationWarning:!0,children:[(0,o.jsx)("h3",{className:"text-xl font-semibold text-card-foreground mb-3 group-hover:text-primary transition-colors leading-tight line-clamp-2 pr-20",children:(0,o.jsx)(a(),{href:`/posts/${e.id}`,className:"block",children:e.title})}),s&&(0,o.jsx)("p",{className:"text-muted-foreground leading-relaxed line-clamp-3 text-sm",children:d})]}),(0,o.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 pt-4 border-t border-border/50",suppressHydrationWarning:!0,children:[(0,o.jsxs)("div",{className:"flex items-center gap-4",suppressHydrationWarning:!0,children:[(0,o.jsxs)("time",{className:"flex items-center gap-2 text-sm text-muted-foreground",title:(0,i.Yq)(e.created_at),children:[(0,o.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})}),(0,i.Yq)(e.created_at)]}),(0,o.jsxs)("span",{className:"inline-flex items-center gap-1 px-2 py-1 rounded-md bg-primary/10 text-primary text-xs font-medium",children:[(0,o.jsx)("svg",{className:"w-3 h-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})}),l]})]}),(0,o.jsxs)(a(),{href:`/posts/${e.id}`,className:"group inline-flex items-center gap-2 text-sm font-medium text-primary hover:text-primary/80 transition-all duration-200",children:["Read more",(0,o.jsx)("svg",{className:"w-4 h-4 group-hover:translate-x-1 transition-transform duration-200",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 8l4 4m0 0l-4 4m4-4H3"})})]})]})]})]})}function d({onSearch:e,placeholder:r="Search posts...",className:t,autoFocus:n=!1}){let[a,l]=(0,s.useState)(""),[d,c]=(0,s.useState)(!1),m=(0,s.useRef)(null),p=()=>{l(""),m.current&&m.current.focus()};return(0,o.jsxs)("div",{className:(0,i.cn)("relative group",t),suppressHydrationWarning:!0,children:[(0,o.jsxs)("div",{className:(0,i.cn)("relative flex items-center transition-all duration-200","bg-background border border-border rounded-lg","hover:border-primary/30 focus-within:border-primary/50","shadow-sm hover:shadow-md focus-within:shadow-md",d&&"ring-2 ring-primary/20"),suppressHydrationWarning:!0,children:[(0,o.jsx)("div",{className:"absolute left-3 flex items-center pointer-events-none",suppressHydrationWarning:!0,children:(0,o.jsx)("svg",{className:(0,i.cn)("w-4 h-4 transition-colors duration-200",d||a?"text-primary":"text-muted-foreground"),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})}),(0,o.jsx)("input",{ref:m,type:"text",value:a,onChange:e=>l(e.target.value),onFocus:()=>c(!0),onBlur:()=>c(!1),onKeyDown:e=>{"Escape"===e.key&&p()},placeholder:r,className:(0,i.cn)("w-full pl-10 pr-10 py-2.5 sm:py-3","bg-transparent border-0 outline-none","text-foreground placeholder:text-muted-foreground","text-sm sm:text-base")}),a&&(0,o.jsx)("button",{onClick:p,className:(0,i.cn)("absolute right-3 flex items-center justify-center","w-5 h-5 rounded-full","text-muted-foreground hover:text-foreground","hover:bg-muted transition-all duration-200","focus:outline-none focus:ring-2 focus:ring-primary/20"),"aria-label":"Clear search",children:(0,o.jsx)("svg",{className:"w-3 h-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),a&&(0,o.jsx)("div",{className:"absolute top-full left-0 right-0 mt-1",children:(0,o.jsxs)("div",{className:"text-xs text-muted-foreground px-3 py-1",children:['Searching for "',a,'"...']})})]})}function c({query:e,totalResults:r,className:t}){return e?(0,o.jsxs)("div",{className:(0,i.cn)("flex items-center justify-between","px-4 py-2 mb-6","bg-muted/50 rounded-lg border border-border",t),children:[(0,o.jsxs)("div",{className:"flex items-center gap-2",children:[(0,o.jsx)("svg",{className:"w-4 h-4 text-muted-foreground",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})}),(0,o.jsx)("span",{className:"text-sm text-foreground",children:0===r?(0,o.jsxs)(o.Fragment,{children:["No results found for ",(0,o.jsxs)("strong",{children:['"',e,'"']})]}):(0,o.jsxs)(o.Fragment,{children:[r," result",1===r?"":"s"," for ",(0,o.jsxs)("strong",{children:['"',e,'"']})]})})]}),0===r&&(0,o.jsx)("div",{className:"text-xs text-muted-foreground",children:"Try different keywords"})]}):null}function m({currentPage:e,totalPages:r,onPageChange:t,className:s,showFirstLast:n=!0,maxVisiblePages:a=5}){if(r<=1)return null;let l=(()=>{let t=[];if(r<=a)for(let e=1;e<=r;e++)t.push(e);else{let o=Math.max(1,e-Math.floor(a/2)),s=Math.min(r,o+a-1);s-o+1<a&&(o=Math.max(1,s-a+1)),o>1&&(t.push(1),o>2&&t.push("..."));for(let e=o;e<=s;e++)t.push(e);s<r&&(s<r-1&&t.push("..."),t.push(r))}return t})(),d=(e=!1,r=!1)=>(0,i.cn)("flex items-center justify-center","min-w-[40px] h-10 px-3","text-sm font-medium","border border-border rounded-lg","transition-all duration-200","focus:outline-none focus:ring-2 focus:ring-primary/20",e?"bg-primary text-primary-foreground border-primary shadow-sm":r?"bg-muted text-muted-foreground cursor-not-allowed":"bg-background text-foreground hover:bg-muted hover:border-primary/30 hover:shadow-sm");return(0,o.jsxs)("nav",{className:(0,i.cn)("flex items-center justify-center gap-1 sm:gap-2",s),"aria-label":"Pagination",children:[n&&e>1&&(0,o.jsx)("button",{onClick:()=>t(1),className:d(!1,!1),"aria-label":"Go to first page",children:(0,o.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 19l-7-7 7-7m8 14l-7-7 7-7"})})}),(0,o.jsxs)("button",{onClick:()=>t(e-1),disabled:e<=1,className:d(!1,e<=1),"aria-label":"Go to previous page",children:[(0,o.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})}),(0,o.jsx)("span",{className:"hidden sm:inline ml-1",children:"Previous"})]}),(0,o.jsx)("div",{className:"flex items-center gap-1",children:l.map((r,s)=>(0,o.jsx)("div",{children:"..."===r?(0,o.jsx)("span",{className:"flex items-center justify-center min-w-[40px] h-10 text-muted-foreground",children:"..."}):(0,o.jsx)("button",{onClick:()=>t(r),className:d(r===e,!1),"aria-label":`Go to page ${r}`,"aria-current":r===e?"page":void 0,children:r})},s))}),(0,o.jsxs)("button",{onClick:()=>t(e+1),disabled:e>=r,className:d(!1,e>=r),"aria-label":"Go to next page",children:[(0,o.jsx)("span",{className:"hidden sm:inline mr-1",children:"Next"}),(0,o.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]}),n&&e<r&&(0,o.jsx)("button",{onClick:()=>t(r),className:d(!1,!1),"aria-label":"Go to last page",children:(0,o.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 5l7 7-7 7M5 5l7 7-7 7"})})})]})}function p({currentPage:e,totalItems:r,itemsPerPage:t,className:s}){let n=Math.min(e*t,r);return(0,o.jsxs)("div",{className:(0,i.cn)("flex items-center justify-center text-sm text-muted-foreground",s),children:["Showing ",(e-1)*t+1," to ",n," of ",r," posts"]})}var u=t(96044);function h(){let[e,r]=(0,s.useState)(new Date);return(0,o.jsx)("section",{className:"mb-8",children:(0,o.jsxs)("div",{className:"bg-card border border-primary/20 rounded-lg p-6 shadow-xl text-center",children:[(0,o.jsxs)("h2",{className:"text-xl font-bold text-foreground mb-4 flex items-center justify-center",children:[(0,o.jsx)("svg",{className:"w-6 h-6 mr-2 text-primary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})}),"Today's Date & Time"]}),(0,o.jsx)("p",{className:"text-2xl font-semibold text-foreground mb-2",children:e.toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"})}),(0,o.jsx)("p",{className:"text-3xl font-extrabold text-primary",children:e.toLocaleTimeString("en-US",{hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!0})})]})})}function x({initialPosts:e,user:r,userIsAdmin:t}){let[n,x]=(0,s.useState)(""),[g,f]=(0,s.useState)(1),b=(0,s.useMemo)(()=>(0,i.CI)(e,n),[e,n]),v=(0,s.useMemo)(()=>(0,i.g7)(b,g,6),[b,g]),k=e.length>0?e[0]:null;return(0,o.jsxs)("div",{className:"min-h-screen bg-background",children:[(0,o.jsx)("header",{className:"bg-card/95 shadow-lg border-b border-border sticky top-0 z-50 backdrop-blur-sm",children:(0,o.jsx)("div",{className:"max-w-7xl mx-auto px-3 sm:px-6 lg:px-8 py-3 sm:py-4 lg:py-6",children:(0,o.jsxs)("div",{className:"flex justify-between items-center",children:[(0,o.jsx)(a(),{href:"/",className:"text-xl sm:text-2xl lg:text-3xl font-bold text-foreground hover:text-primary transition-colors",children:"My Blog"}),(0,o.jsxs)("div",{className:"flex items-center gap-1 sm:gap-2 lg:gap-4",children:[(0,o.jsx)(u.ClientThemeToggle,{}),r?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)("span",{className:"hidden lg:block text-sm text-muted-foreground truncate max-w-32",children:["Welcome, ",r.email]}),t&&(0,o.jsxs)(a(),{href:"/admin/new-post",className:"bg-primary text-primary-foreground px-2 sm:px-3 lg:px-4 py-2 rounded-md hover:bg-primary/90 transition-all duration-200 text-sm font-medium shadow-sm hover:shadow-md touch-manipulation min-h-[44px] flex items-center justify-center",children:[(0,o.jsx)("span",{className:"hidden sm:inline",children:"New Post"}),(0,o.jsx)("span",{className:"sm:hidden text-lg",children:"+"})]}),t&&(0,o.jsxs)(a(),{href:"/admin/manage-posts",className:"bg-secondary text-secondary-foreground px-2 sm:px-3 lg:px-4 py-2 rounded-md hover:bg-secondary/80 transition-all duration-200 text-sm font-medium shadow-sm hover:shadow-md touch-manipulation min-h-[44px] flex items-center justify-center",children:[(0,o.jsx)("span",{className:"hidden sm:inline",children:"Manage"}),(0,o.jsx)("span",{className:"sm:hidden text-lg",children:"⚙️"})]}),(0,o.jsx)("form",{action:"/auth/signout",method:"post",children:(0,o.jsxs)("button",{type:"submit",className:"inline-flex items-center justify-center px-2 sm:px-3 py-2 rounded-md text-sm font-medium transition-colors bg-destructive text-destructive-foreground hover:bg-destructive/90 shadow-sm hover:shadow-md touch-manipulation min-h-[44px]",children:[(0,o.jsx)("svg",{className:"w-4 h-4 sm:mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"})}),(0,o.jsx)("span",{className:"hidden sm:inline",children:"Sign Out"})]})})]}):(0,o.jsx)(a(),{href:"/login",className:"bg-primary text-primary-foreground px-3 sm:px-4 py-2 rounded-md hover:bg-primary/90 transition-all duration-200 text-sm font-medium shadow-sm hover:shadow-md touch-manipulation min-h-[44px] flex items-center justify-center",children:"Sign In"})]})]})})}),(0,o.jsx)("main",{className:"max-w-7xl mx-auto px-3 sm:px-6 lg:px-8 py-8 sm:py-12 lg:py-16",children:0===e.length?(0,o.jsx)("div",{className:"text-center py-16 lg:py-24",children:(0,o.jsxs)("div",{className:"max-w-md mx-auto",children:[(0,o.jsx)("div",{className:"w-16 h-16 mx-auto mb-6 bg-muted rounded-full flex items-center justify-center",children:(0,o.jsx)("svg",{className:"w-8 h-8 text-muted-foreground",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),(0,o.jsx)("h2",{className:"text-2xl font-semibold text-foreground mb-2",children:"No posts yet"}),(0,o.jsx)("p",{className:"text-muted-foreground mb-6",children:"Start sharing your thoughts with the world!"}),t&&(0,o.jsxs)(a(),{href:"/admin/new-post",className:"inline-flex items-center px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-all duration-200 font-medium shadow-sm hover:shadow-md",children:[(0,o.jsx)("svg",{className:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})}),"Create your first post"]})]})}):(0,o.jsx)(o.Fragment,{children:(0,o.jsxs)("div",{className:"flex flex-col lg:flex-row gap-8 lg:gap-12",children:[(0,o.jsxs)("div",{className:"flex-1 lg:w-2/3 order-2 lg:order-1",children:[(0,o.jsxs)("section",{id:"posts",className:"mb-8 sm:mb-12",children:[(0,o.jsx)("div",{className:"flex flex-col gap-4 sm:gap-6 mb-6 sm:mb-8",children:(0,o.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-center gap-4 sm:gap-6",children:[(0,o.jsxs)("div",{className:"text-center sm:text-left",children:[(0,o.jsx)("h2",{className:"text-3xl sm:text-4xl font-extrabold text-foreground mb-2",children:"All Posts"}),(0,o.jsxs)("p",{className:"text-lg text-muted-foreground",children:[e.length," post",1===e.length?"":"s"," available"]})]}),(0,o.jsx)("div",{className:"w-full sm:max-w-xs",children:(0,o.jsx)(d,{onSearch:e=>{x(e),f(1)},className:"text-sm"})})]})}),(0,o.jsx)(c,{query:n,totalResults:b.length})]}),(0,o.jsx)("section",{className:"mb-16",children:0===v.items.length?(0,o.jsxs)("div",{className:"text-center py-20",children:[(0,o.jsx)("div",{className:"w-20 h-20 mx-auto mb-8 bg-gradient-to-br from-muted to-muted/50 rounded-2xl flex items-center justify-center",children:(0,o.jsx)("svg",{className:"w-10 h-10 text-muted-foreground",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})}),(0,o.jsx)("h3",{className:"text-2xl font-semibold text-foreground mb-3",children:"No posts found"}),(0,o.jsx)("p",{className:"text-muted-foreground text-lg mb-6",children:"Try adjusting your search terms or browse all posts"}),(0,o.jsx)(a(),{href:"#posts",onClick:()=>{x(""),f(1)},className:"inline-flex items-center px-6 py-3 rounded-lg bg-primary text-primary-foreground font-medium hover:bg-primary/90 transition-colors",children:"Clear Search"})]}):(0,o.jsx)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-2",children:v.items.map((e,r)=>(0,o.jsx)(l,{post:e,className:"animate-slide-up hover:scale-[1.02] transition-transform duration-300",style:{animationDelay:`${.05*r}s`}},e.id))})}),v.totalPages>1&&(0,o.jsxs)("section",{className:"mb-12 space-y-6",children:[(0,o.jsx)(m,{currentPage:v.currentPage,totalPages:v.totalPages,onPageChange:f}),(0,o.jsx)(p,{currentPage:v.currentPage,totalPages:v.totalPages,totalItems:v.totalItems,itemsPerPage:6})]})]}),(0,o.jsxs)("aside",{className:"lg:w-1/3 lg:max-w-sm order-1 lg:order-2",children:[(0,o.jsx)(h,{}),e.length>1&&(0,o.jsx)("section",{className:"mb-8",children:(0,o.jsxs)("div",{className:"bg-card border border-border rounded-lg p-6 shadow-sm",children:[(0,o.jsxs)("h3",{className:"text-lg font-semibold text-foreground mb-4 flex items-center",children:[(0,o.jsx)("svg",{className:"w-5 h-5 mr-2 text-primary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})}),"Recent Posts"]}),(0,o.jsx)("div",{className:"space-y-4",children:e.filter(e=>e.id!==k?.id).slice(0,3).map(e=>(0,o.jsx)("div",{className:"group",children:(0,o.jsxs)(a(),{href:`/posts/${e.id}`,className:"block",children:[(0,o.jsx)("h4",{className:"text-sm font-medium text-foreground group-hover:text-primary transition-colors line-clamp-2 mb-1",children:e.title}),(0,o.jsx)("p",{className:"text-xs text-muted-foreground",children:(0,i.Yq)(e.created_at)})]})},e.id))})]})})]})]})})})]})}},52331:(e,r,t)=>{Promise.resolve().then(t.bind(t,96871))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},61135:()=>{},62688:(e,r,t)=>{"use strict";t.d(r,{A:()=>m});var o=t(43210);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,t)=>t?t.toUpperCase():r.toLowerCase()),a=e=>{let r=n(e);return r.charAt(0).toUpperCase()+r.slice(1)},i=(...e)=>e.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim(),l=e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,o.forwardRef)(({color:e="currentColor",size:r=24,strokeWidth:t=2,absoluteStrokeWidth:s,className:n="",children:a,iconNode:c,...m},p)=>(0,o.createElement)("svg",{ref:p,...d,width:r,height:r,stroke:e,strokeWidth:s?24*Number(t)/Number(r):t,className:i("lucide",n),...!a&&!l(m)&&{"aria-hidden":"true"},...m},[...c.map(([e,r])=>(0,o.createElement)(e,r)),...Array.isArray(a)?a:[a]])),m=(e,r)=>{let t=(0,o.forwardRef)(({className:t,...n},l)=>(0,o.createElement)(c,{ref:l,iconNode:r,className:i(`lucide-${s(a(e))}`,`lucide-${e}`,t),...n}));return t.displayName=a(e),t}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67393:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n});var o=t(37413),s=t(36328);function n(){return(0,o.jsx)(s.default,{})}},70440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});var o=t(31658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,o.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},78592:(e,r,t)=>{Promise.resolve().then(t.bind(t,36328))},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83701:(e,r,t)=>{"use strict";t.d(r,{ThemeProvider:()=>o});let o=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\theme-provider.tsx","ThemeProvider")},85578:(e,r,t)=>{"use strict";t.d(r,{default:()=>s});var o=t(60687);function s(){return(0,o.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,o.jsxs)("div",{className:"relative",children:[(0,o.jsx)("div",{className:"h-24 w-24 rounded-full border-t-8 border-b-8 border-gray-200"}),(0,o.jsx)("div",{className:"absolute top-0 left-0 h-24 w-24 rounded-full border-t-8 border-b-8 border-blue-500 animate-spin"})]})})}t(43210)},91645:e=>{"use strict";e.exports=require("net")},92083:(e,r,t)=>{Promise.resolve().then(t.bind(t,83701))},94340:(e,r,t)=>{"use strict";t.d(r,{HomePageClient:()=>o});let o=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call HomePageClient() from the server but HomePageClient is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\homepage-client.tsx","HomePageClient")},94431:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>c,metadata:()=>d});var o=t(37413),s=t(22376),n=t.n(s),a=t(68726),i=t.n(a);t(61135);var l=t(83701);let d={title:"My Blog",description:"A modern blog built with Next.js and Supabase",icons:{icon:"/icon.png"}};function c({children:e}){return(0,o.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,o.jsxs)("body",{className:`${n().variable} ${i().variable} antialiased`,suppressHydrationWarning:!0,children:[(0,o.jsx)("meta",{name:"google-site-verification",content:"aY1T1sPS66KuuGAAe1zzVIZjS7YdM4MFbue__-3YBkI"}),(0,o.jsx)(l.ThemeProvider,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,suppressHydrationWarning:!0,children:e})]})})}},94735:e=>{"use strict";e.exports=require("events")},96044:(e,r,t)=>{"use strict";t.d(r,{ClientThemeToggle:()=>n});var o=t(60687),s=t(43984);function n(){return(0,o.jsx)(s.ThemeToggle,{})}},96871:(e,r,t)=>{"use strict";t.d(r,{ThemeProvider:()=>n});var o=t(60687);t(43210);var s=t(10218);function n({children:e,...r}){return(0,o.jsx)(s.N,{...r,children:e})}},98575:(e,r,t)=>{"use strict";t.d(r,{HeroSkeleton:()=>n,PostGridSkeleton:()=>s});var o=t(12907);(0,o.registerClientReference)(function(){throw Error("Attempted to call Skeleton() from the server but Skeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\skeleton.tsx","Skeleton"),(0,o.registerClientReference)(function(){throw Error("Attempted to call PostCardSkeleton() from the server but PostCardSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\skeleton.tsx","PostCardSkeleton");let s=(0,o.registerClientReference)(function(){throw Error("Attempted to call PostGridSkeleton() from the server but PostGridSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\skeleton.tsx","PostGridSkeleton"),n=(0,o.registerClientReference)(function(){throw Error("Attempted to call HeroSkeleton() from the server but HeroSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\skeleton.tsx","HeroSkeleton");(0,o.registerClientReference)(function(){throw Error("Attempted to call SearchBarSkeleton() from the server but SearchBarSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\skeleton.tsx","SearchBarSkeleton"),(0,o.registerClientReference)(function(){throw Error("Attempted to call FeaturedPostSkeleton() from the server but FeaturedPostSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\skeleton.tsx","FeaturedPostSkeleton"),(0,o.registerClientReference)(function(){throw Error("Attempted to call FilterSkeleton() from the server but FilterSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\skeleton.tsx","FilterSkeleton"),(0,o.registerClientReference)(function(){throw Error("Attempted to call PaginationSkeleton() from the server but PaginationSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\skeleton.tsx","PaginationSkeleton")}};var r=require("../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[447,98,567,866],()=>t(20077));module.exports=o})();