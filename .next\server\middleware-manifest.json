{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_f58e8655._.js", "server/edge/chunks/[root-of-the-server]__c6c91fdf._.js", "server/edge/chunks/edge-wrapper_c6630286.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "vIkrLhuFejbE7f2q9zDPJdA0q6VuXpH+EB9OFkD5UQc=", "__NEXT_PREVIEW_MODE_ID": "6a3ee094752368cb3a52f1235d5093b7", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "9a2be70d61981b44fc448376d3311c5d0677e038d9a45399121f201b553144ed", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "c70277cb3316e1721cff687ecf3d5fcaaf0d2bc97619dcc7a86266cbd3cabcb3"}}}, "sortedMiddleware": ["/"], "functions": {}}