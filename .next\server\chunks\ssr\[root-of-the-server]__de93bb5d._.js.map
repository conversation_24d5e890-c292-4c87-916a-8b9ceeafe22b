{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/theme-toggle.tsx"], "sourcesContent": ["'use client'\n\nimport * as React from 'react'\nimport { Moon, Sun } from 'lucide-react'\nimport { useTheme } from 'next-themes'\n\nexport function ThemeToggle() {\n  const { theme, setTheme } = useTheme()\n  const [mounted, setMounted] = React.useState(false)\n\n  React.useEffect(() => {\n    setMounted(true)\n  }, [])\n\n  if (!mounted) {\n    return (\n      <button className=\"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 w-10\">\n        <Sun className=\"h-[1.2rem] w-[1.2rem]\" />\n        <span className=\"sr-only\">切換主題</span>\n      </button>\n    )\n  }\n\n  return (\n    <button\n      onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}\n      className=\"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 w-10\"\n    >\n      {theme === 'light' ? (\n        <Moon className=\"h-[1.2rem] w-[1.2rem]\" />\n      ) : (\n        <Sun className=\"h-[1.2rem] w-[1.2rem]\" />\n      )}\n      <span className=\"sr-only\">切換主題</span>\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAJA;;;;;AAMO,SAAS;IACd,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,WAAW;IACb,GAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YAAO,WAAU;;8BAChB,8OAAC,gMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;8BACf,8OAAC;oBAAK,WAAU;8BAAU;;;;;;;;;;;;IAGhC;IAEA,qBACE,8OAAC;QACC,SAAS,IAAM,SAAS,UAAU,UAAU,SAAS;QACrD,WAAU;;YAET,UAAU,wBACT,8OAAC,kMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;qCAEhB,8OAAC,gMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;0BAEjB,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/client-theme-toggle.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { ThemeToggle } from './theme-toggle'\r\n\r\nexport function ClientThemeToggle() {\r\n  return <ThemeToggle />\r\n}"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIO,SAAS;IACd,qBAAO,8OAAC,qIAAA,CAAA,cAAW;;;;;AACrB", "debugId": null}}, {"offset": {"line": 223, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/lib/supabase/client.ts"], "sourcesContent": ["import { createBrowserClient } from '@supabase/ssr'\n\nexport function createClient() {\n  const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'\n  return createBrowserClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\n    {\n      cookieOptions: {\n        domain: new URL(siteUrl).hostname,\n        sameSite: 'lax',\n        secure: false\n      },\n      auth: {\n        autoRefreshToken: true,\n        persistSession: true,\n        detectSessionInUrl: true\n      }\n    }\n  )\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAEO,SAAS;IACd,MAAM,UAAU,0EAAoC;IACpD,OAAO,CAAA,GAAA,0KAAA,CAAA,sBAAmB,AAAD,sUAGvB;QACE,eAAe;YACb,QAAQ,IAAI,IAAI,SAAS,QAAQ;YACjC,UAAU;YACV,QAAQ;QACV;QACA,MAAM;YACJ,kBAAkB;YAClB,gBAAgB;YAChB,oBAAoB;QACtB;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 250, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/app/login/login-form.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState } from 'react'\r\nimport { createClient } from '@/lib/supabase/client'\r\nimport { useRouter } from 'next/navigation'\r\n\r\nexport default function LoginForm() {\r\n  const [email, setEmail] = useState('')\r\n  const [password, setPassword] = useState('')\r\n  const [loading, setLoading] = useState(false)\r\n  const [message, setMessage] = useState('')\r\n  const [isSignUp, setIsSignUp] = useState(false)\r\n  const router = useRouter()\r\n  const supabase = createClient()\r\n\r\n  const handleAuth = async (e: React.FormEvent) => {\r\n    e.preventDefault()\r\n    setLoading(true)\r\n    setMessage('')\r\n\r\n    try {\r\n      if (isSignUp) {\r\n        const { error } = await supabase.auth.signUp({\r\n          email,\r\n          password,\r\n        })\r\n        if (error) throw error\r\n        setMessage('Registration successful! Please check your email to verify your account.')\r\n      } else {\r\n        const { error } = await supabase.auth.signInWithPassword({\r\n          email,\r\n          password,\r\n        })\r\n        if (error) throw error\r\n        router.push('/')\r\n        router.refresh()\r\n      }\r\n    } catch (error) {\r\n      setMessage(error instanceof Error ? error.message : 'An error occurred')\r\n    } finally {\r\n      setLoading(false)\r\n    }\r\n  }\r\n\r\n  return (\r\n    <form className=\"space-y-6\" onSubmit={handleAuth}>\r\n      <div className=\"space-y-5\">\r\n        <div>\r\n          <label htmlFor=\"email\" className=\"block text-sm font-medium text-foreground mb-2\">\r\n            Email address\r\n          </label>\r\n          <input\r\n            id=\"email\"\r\n            name=\"email\"\r\n            type=\"email\"\r\n            autoComplete=\"email\"\r\n            required\r\n            className=\"w-full px-4 py-3 border border-input bg-background text-foreground rounded-lg focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent transition-all duration-200 placeholder:text-muted-foreground\"\r\n            placeholder=\"Enter your email address\"\r\n            value={email}\r\n            onChange={(e) => setEmail(e.target.value)}\r\n          />\r\n        </div>\r\n        <div>\r\n          <label htmlFor=\"password\" className=\"block text-sm font-medium text-foreground mb-2\">\r\n            Password\r\n          </label>\r\n          <input\r\n            id=\"password\"\r\n            name=\"password\"\r\n            type=\"password\"\r\n            autoComplete=\"current-password\"\r\n            required\r\n            className=\"w-full px-4 py-3 border border-input bg-background text-foreground rounded-lg focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent transition-all duration-200 placeholder:text-muted-foreground\"\r\n            placeholder=\"Enter your password\"\r\n            value={password}\r\n            onChange={(e) => setPassword(e.target.value)}\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      {message && (\r\n        <div className={`text-sm text-center p-4 rounded-lg ${\r\n          message.includes('successful')\r\n            ? 'bg-green-50 text-green-700 border border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800'\r\n            : 'bg-destructive/10 text-destructive border border-destructive/20'\r\n        }`}>\r\n          {message}\r\n        </div>\r\n      )}\r\n\r\n      <div>\r\n        <button\r\n          type=\"submit\"\r\n          disabled={loading}\r\n          className=\"w-full bg-primary text-primary-foreground py-3 px-4 rounded-lg hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-medium shadow-sm hover:shadow-md\"\r\n        >\r\n          {loading ? (\r\n            <div className=\"flex items-center justify-center\">\r\n              <div className=\"w-5 h-5 border-2 border-current border-t-transparent rounded-full animate-spin mr-2\"></div>\r\n              Processing...\r\n            </div>\r\n          ) : (\r\n            isSignUp ? 'Create Account' : 'Sign In'\r\n          )}\r\n        </button>\r\n      </div>\r\n\r\n      <div className=\"text-center\">\r\n        <button\r\n          type=\"button\"\r\n          onClick={() => setIsSignUp(!isSignUp)}\r\n          className=\"text-primary hover:text-primary/80 text-sm transition-colors font-medium\"\r\n        >\r\n          {isSignUp ? 'Already have an account? Sign in' : \"Don't have an account? Sign up\"}\r\n        </button>\r\n      </div>\r\n    </form>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,aAAa,OAAO;QACxB,EAAE,cAAc;QAChB,WAAW;QACX,WAAW;QAEX,IAAI;YACF,IAAI,UAAU;gBACZ,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC;oBAC3C;oBACA;gBACF;gBACA,IAAI,OAAO,MAAM;gBACjB,WAAW;YACb,OAAO;gBACL,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC;oBACvD;oBACA;gBACF;gBACA,IAAI,OAAO,MAAM;gBACjB,OAAO,IAAI,CAAC;gBACZ,OAAO,OAAO;YAChB;QACF,EAAE,OAAO,OAAO;YACd,WAAW,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACtD,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,8OAAC;QAAK,WAAU;QAAY,UAAU;;0BACpC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAM,SAAQ;gCAAQ,WAAU;0CAAiD;;;;;;0CAGlF,8OAAC;gCACC,IAAG;gCACH,MAAK;gCACL,MAAK;gCACL,cAAa;gCACb,QAAQ;gCACR,WAAU;gCACV,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;kCAG5C,8OAAC;;0CACC,8OAAC;gCAAM,SAAQ;gCAAW,WAAU;0CAAiD;;;;;;0CAGrF,8OAAC;gCACC,IAAG;gCACH,MAAK;gCACL,MAAK;gCACL,cAAa;gCACb,QAAQ;gCACR,WAAU;gCACV,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;YAKhD,yBACC,8OAAC;gBAAI,WAAW,CAAC,mCAAmC,EAClD,QAAQ,QAAQ,CAAC,gBACb,sHACA,mEACJ;0BACC;;;;;;0BAIL,8OAAC;0BACC,cAAA,8OAAC;oBACC,MAAK;oBACL,UAAU;oBACV,WAAU;8BAET,wBACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;4BAA4F;;;;;;+BAI7G,WAAW,mBAAmB;;;;;;;;;;;0BAKpC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,MAAK;oBACL,SAAS,IAAM,YAAY,CAAC;oBAC5B,WAAU;8BAET,WAAW,qCAAqC;;;;;;;;;;;;;;;;;AAK3D", "debugId": null}}]}