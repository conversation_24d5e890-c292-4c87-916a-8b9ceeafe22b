{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/cherry-editor.tsx"], "sourcesContent": ["'use client'\n\nimport dynamic from 'next/dynamic'\nimport { useState, useEffect } from 'react'\n\n// Dynamically import MDEditor to avoid SSR issues\nconst MDEditor = dynamic(\n  () => import('@uiw/react-md-editor'),\n  {\n    ssr: false,\n    loading: () => (\n      <div className=\"w-full p-4 border border-input bg-background text-foreground rounded-md animate-pulse min-h-[400px] flex items-center justify-center\">\n        <div className=\"text-muted-foreground\">Loading editor...</div>\n      </div>\n    )\n  }\n)\n\ninterface CherryEditorProps {\n  value?: string\n  onChange?: (value: string) => void\n  minHeight?: string\n  placeholder?: string\n}\n\nexport function CherryEditor({\n  value = '',\n  onChange,\n  minHeight = '500px',\n  placeholder = 'Start writing your amazing content...'\n}: CherryEditorProps) {\n  const [mounted, setMounted] = useState(false)\n\n  useEffect(() => {\n    setMounted(true)\n  }, [])\n\n  // Show loading state during SSR\n  if (!mounted) {\n    return (\n      <div\n        className=\"w-full p-4 border border-input bg-background text-foreground rounded-md animate-pulse flex items-center justify-center\"\n        style={{ minHeight }}\n      >\n        <div className=\"text-muted-foreground\">Loading editor...</div>\n      </div>\n    )\n  }\n\n\n\n  try {\n    return (\n      <div\n        className=\"w-full md-editor-wrapper\"\n        style={{ minHeight }}\n        data-color-mode=\"auto\"\n      >\n        <MDEditor\n          value={value}\n          onChange={(val) => onChange?.(val || '')}\n          preview=\"live\"\n          hideToolbar={false}\n          visibleDragbar={false}\n          height={parseInt(minHeight)}\n          textareaProps={{\n            placeholder,\n            style: {\n              fontSize: 15,\n              lineHeight: 1.7,\n              fontFamily: 'ui-monospace, SFMono-Regular, \"SF Mono\", Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace',\n              color: 'inherit',\n              padding: '16px'\n            }\n          }}\n          style={{\n            backgroundColor: 'hsl(var(--background))',\n            color: 'hsl(var(--foreground))',\n            minHeight: minHeight,\n            width: '100%'\n          }}\n        />\n      </div>\n    )\n  } catch (error) {\n    console.error('Error rendering MDEditor:', error)\n    return (\n      <div className=\"w-full p-4 border border-input bg-background text-foreground rounded-md\">\n        <div className=\"text-red-500 mb-2\">Error loading editor</div>\n        <textarea\n          value={value}\n          onChange={(e) => onChange?.(e.target.value)}\n          placeholder={placeholder}\n          className=\"w-full h-full bg-transparent border-0 outline-0 resize-none text-foreground\"\n          style={{ minHeight: `calc(${minHeight} - 2rem)` }}\n        />\n      </div>\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;;AAHA;;;AAKA,kDAAkD;AAClD,MAAM,WAAW,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EACrB;;;;;;IAEE,KAAK;IACL,SAAS,kBACP,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BAAwB;;;;;;;;;;;;KANzC;AAmBC,SAAS,aAAa,EAC3B,QAAQ,EAAE,EACV,QAAQ,EACR,YAAY,OAAO,EACnB,cAAc,uCAAuC,EACnC;;IAClB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,WAAW;QACb;iCAAG,EAAE;IAEL,gCAAgC;IAChC,IAAI,CAAC,SAAS;QACZ,qBACE,6LAAC;YACC,WAAU;YACV,OAAO;gBAAE;YAAU;sBAEnB,cAAA,6LAAC;gBAAI,WAAU;0BAAwB;;;;;;;;;;;IAG7C;IAIA,IAAI;QACF,qBACE,6LAAC;YACC,WAAU;YACV,OAAO;gBAAE;YAAU;YACnB,mBAAgB;sBAEhB,cAAA,6LAAC;gBACC,OAAO;gBACP,UAAU,CAAC,MAAQ,WAAW,OAAO;gBACrC,SAAQ;gBACR,aAAa;gBACb,gBAAgB;gBAChB,QAAQ,SAAS;gBACjB,eAAe;oBACb;oBACA,OAAO;wBACL,UAAU;wBACV,YAAY;wBACZ,YAAY;wBACZ,OAAO;wBACP,SAAS;oBACX;gBACF;gBACA,OAAO;oBACL,iBAAiB;oBACjB,OAAO;oBACP,WAAW;oBACX,OAAO;gBACT;;;;;;;;;;;IAIR,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BAAoB;;;;;;8BACnC,6LAAC;oBACC,OAAO;oBACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;oBAC1C,aAAa;oBACb,WAAU;oBACV,OAAO;wBAAE,WAAW,CAAC,KAAK,EAAE,UAAU,QAAQ,CAAC;oBAAC;;;;;;;;;;;;IAIxD;AACF;GA1EgB;MAAA", "debugId": null}}, {"offset": {"line": 161, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/lib/supabase/client.ts"], "sourcesContent": ["import { createBrowserClient } from '@supabase/ssr'\r\n\r\nexport function createClient() {\r\n  // Detect if we're running on localhost\r\n  const isLocalhost = typeof window !== 'undefined' &&\r\n    (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1')\r\n\r\n  const cookieOptions = {\r\n    // Let browser handle domain automatically - don't set domain\r\n    sameSite: 'lax' as const,\r\n    secure: !isLocalhost, // Only use secure cookies in production\r\n    path: '/',\r\n  }\r\n\r\n  // Debug logging\r\n  if (typeof window !== 'undefined') {\r\n    console.log('Supabase client config:', {\r\n      hostname: window.location.hostname,\r\n      isLocalhost,\r\n      cookieOptions\r\n    })\r\n  }\r\n\r\n  return createBrowserClient(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\r\n    {\r\n      cookieOptions,\r\n      auth: {\r\n        autoRefreshToken: true,\r\n        persistSession: true,\r\n        detectSessionInUrl: true\r\n      }\r\n    }\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;AAwBI;AAxBJ;AAAA;;AAEO,SAAS;IACd,uCAAuC;IACvC,MAAM,cAAc,aAAkB,eACpC,CAAC,OAAO,QAAQ,CAAC,QAAQ,KAAK,eAAe,OAAO,QAAQ,CAAC,QAAQ,KAAK,WAAW;IAEvF,MAAM,gBAAgB;QACpB,6DAA6D;QAC7D,UAAU;QACV,QAAQ,CAAC;QACT,MAAM;IACR;IAEA,gBAAgB;IAChB,wCAAmC;QACjC,QAAQ,GAAG,CAAC,2BAA2B;YACrC,UAAU,OAAO,QAAQ,CAAC,QAAQ;YAClC;YACA;QACF;IACF;IAEA,OAAO,CAAA,GAAA,6KAAA,CAAA,sBAAmB,AAAD,sUAGvB;QACE;QACA,MAAM;YACJ,kBAAkB;YAClB,gBAAgB;YAChB,oBAAoB;QACtB;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 203, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/app/admin/new-post/new-post-form.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport Link from 'next/link'\nimport { CherryEditor } from '@/components/cherry-editor'\nimport { createClient } from '@/lib/supabase/client'\n\nexport default function NewPostForm() {\n  const [title, setTitle] = useState('')\n  const [content, setContent] = useState('')\n  const [isSubmitting, setIsSubmitting] = useState(false)\n  const [error, setError] = useState('')\n  const router = useRouter()\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    \n    if (!title.trim() || !content.trim()) {\n      setError('Title and content are required')\n      return\n    }\n\n    setIsSubmitting(true)\n    setError('')\n\n    try {\n      const supabase = createClient()\n      \n      const { error: insertError } = await supabase\n        .from('posts')\n        .insert([\n          {\n            title: title.trim(),\n            content: content.trim()\n          }\n        ])\n\n      if (insertError) {\n        throw insertError\n      }\n\n      // Redirect to homepage on success\n      router.push('/')\n      router.refresh()\n    } catch (err) {\n      console.error('Error creating post:', err)\n      setError('Error creating post. Please try again.')\n    } finally {\n      setIsSubmitting(false)\n    }\n  }\n\n  return (\n    <div className=\"animate-fade-in\">\n      <form onSubmit={handleSubmit} className=\"space-y-8\">\n        {error && (\n          <div className=\"bg-destructive/10 border border-destructive/20 text-destructive px-6 py-4 rounded-xl animate-slide-in shadow-sm\">\n            <div className=\"flex items-center\">\n              <div className=\"w-10 h-10 bg-destructive/10 rounded-full flex items-center justify-center mr-3\">\n                <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                </svg>\n              </div>\n              <div>\n                <h4 className=\"font-medium\">Error</h4>\n                <p className=\"text-sm\">{error}</p>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Main Form Card */}\n        <div className=\"bg-card rounded-2xl border border-border shadow-xl overflow-hidden\">\n          {/* Card Header */}\n          <div className=\"bg-gradient-to-r from-primary/5 to-secondary/5 px-6 py-4 border-b border-border flex items-center justify-between\">\n            <div>\n              <h2 className=\"text-lg font-semibold text-foreground flex items-center\">\n                <div className=\"w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center mr-3\">\n                  <svg className=\"w-4 h-4 text-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 4v16m8-8H4\" />\n                  </svg>\n                </div>\n                Create New Post\n              </h2>\n              <p className=\"text-sm text-muted-foreground mt-1\">Share your thoughts with the world using our markdown editor</p>\n            </div>\n            <div className=\"flex items-center gap-3\">\n              <Link\n                href=\"/\"\n                className=\"bg-secondary text-secondary-foreground px-4 py-2 rounded-lg hover:bg-secondary/80 transition-all duration-200 font-medium shadow-sm hover:shadow-md inline-flex items-center justify-center group text-xs\"\n              >\n                <svg className=\"w-3 h-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                </svg>\n                Cancel\n              </Link>\n              <button\n                type=\"submit\"\n                disabled={isSubmitting || !title.trim() || !content.trim()}\n                className=\"bg-primary text-primary-foreground px-4 py-2 rounded-lg hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-medium shadow-md hover:shadow-lg inline-flex items-center justify-center group text-xs\"\n              >\n                {isSubmitting ? (\n                  <>\n                    <div className=\"w-3 h-3 border-2 border-current border-t-transparent rounded-full animate-spin mr-2\"></div>\n                    Publishing...\n                  </>\n                ) : (\n                  <>\n                    <svg className=\"w-3 h-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\" />\n                    </svg>\n                    Publish Post\n                  </>\n                )}\n              </button>\n            </div>\n          </div>\n\n          {/* Form Fields */}\n          <div className=\"p-6 space-y-6\">\n            {/* Title Field */}\n            <div className=\"space-y-2\">\n              <label htmlFor=\"title\" className=\"flex items-center text-xs font-medium text-foreground\">\n                <div className=\"w-5 h-5 bg-primary/10 rounded-md flex items-center justify-center mr-2\">\n                  <svg className=\"w-3 h-3 text-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a.997.997 0 01-1.414 0l-7-7A1.997 1.997 0 013 12V7a4 4 0 014-4z\" />\n                  </svg>\n                </div>\n                Post Title\n                <span className=\"text-destructive ml-1\">*</span>\n              </label>\n              <div className=\"relative\">\n                <input\n                  type=\"text\"\n                  id=\"title\"\n                  value={title}\n                  onChange={(e) => setTitle(e.target.value)}\n                  className=\"w-full px-3 py-3 border border-input bg-background text-foreground rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary transition-all duration-200 text-base placeholder:text-muted-foreground/60\"\n                  placeholder=\"Enter an engaging title for your post...\"\n                  disabled={isSubmitting}\n                />\n                <div className=\"absolute right-3 top-1/2 -translate-y-1/2 text-xs text-muted-foreground\">\n                  {title.length}/100\n                </div>\n              </div>\n            </div>\n\n            {/* Content Field */}\n            <div className=\"space-y-2\">\n              <label htmlFor=\"content\" className=\"flex items-center text-xs font-medium text-foreground\">\n                <div className=\"w-5 h-5 bg-primary/10 rounded-md flex items-center justify-center mr-2\">\n                  <svg className=\"w-3 h-3 text-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\" />\n                  </svg>\n                </div>\n                Post Content\n                <span className=\"text-destructive ml-1\">*</span>\n              </label>\n              <div className=\"border border-input rounded-lg overflow-hidden shadow-sm bg-background\">\n                <CherryEditor\n                  value={content}\n                  onChange={setContent}\n                  minHeight=\"600px\"\n                  placeholder=\"Start writing your amazing content...\"\n                />\n              </div>\n\n              {/* Content Stats */}\n              <div className=\"flex items-center justify-between text-xs text-muted-foreground mt-2 px-1\">\n                <span>Markdown supported</span>\n                <div className=\"flex items-center gap-3\">\n                  <span>{content.split(' ').length} words</span>\n                  <span>{content.length} characters</span>\n                  <span>{Math.ceil(content.split(' ').length / 200)} min read</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n      </form>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAQe,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,QAAQ,IAAI,IAAI;YACpC,SAAS;YACT;QACF;QAEA,gBAAgB;QAChB,SAAS;QAET,IAAI;YACF,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;YAE5B,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,SACL,MAAM,CAAC;gBACN;oBACE,OAAO,MAAM,IAAI;oBACjB,SAAS,QAAQ,IAAI;gBACvB;aACD;YAEH,IAAI,aAAa;gBACf,MAAM;YACR;YAEA,kCAAkC;YAClC,OAAO,IAAI,CAAC;YACZ,OAAO,OAAO;QAChB,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,wBAAwB;YACtC,SAAS;QACX,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAK,UAAU;YAAc,WAAU;;gBACrC,uBACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjE,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;0CAGzE,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAc;;;;;;kDAC5B,6LAAC;wCAAE,WAAU;kDAAW;;;;;;;;;;;;;;;;;;;;;;;8BAOhC,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;wDAAuB,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEAC9E,cAAA,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;gDAEnE;;;;;;;sDAGR,6LAAC;4CAAE,WAAU;sDAAqC;;;;;;;;;;;;8CAEpD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,6LAAC;oDAAI,WAAU;oDAAe,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACtE,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;gDACjE;;;;;;;sDAGR,6LAAC;4CACC,MAAK;4CACL,UAAU,gBAAgB,CAAC,MAAM,IAAI,MAAM,CAAC,QAAQ,IAAI;4CACxD,WAAU;sDAET,6BACC;;kEACE,6LAAC;wDAAI,WAAU;;;;;;oDAA4F;;6EAI7G;;kEACE,6LAAC;wDAAI,WAAU;wDAAe,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEACtE,cAAA,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;oDACjE;;;;;;;;;;;;;;;;;;;;sCAShB,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,SAAQ;4CAAQ,WAAU;;8DAC/B,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;wDAAuB,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEAC9E,cAAA,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;gDAEnE;8DAEN,6LAAC;oDAAK,WAAU;8DAAwB;;;;;;;;;;;;sDAE1C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,MAAK;oDACL,IAAG;oDACH,OAAO;oDACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oDACxC,WAAU;oDACV,aAAY;oDACZ,UAAU;;;;;;8DAEZ,6LAAC;oDAAI,WAAU;;wDACZ,MAAM,MAAM;wDAAC;;;;;;;;;;;;;;;;;;;8CAMpB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,SAAQ;4CAAU,WAAU;;8DACjC,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;wDAAuB,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEAC9E,cAAA,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;gDAEnE;8DAEN,6LAAC;oDAAK,WAAU;8DAAwB;;;;;;;;;;;;sDAE1C,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,yIAAA,CAAA,eAAY;gDACX,OAAO;gDACP,UAAU;gDACV,WAAU;gDACV,aAAY;;;;;;;;;;;sDAKhB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAK;;;;;;8DACN,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;gEAAM,QAAQ,KAAK,CAAC,KAAK,MAAM;gEAAC;;;;;;;sEACjC,6LAAC;;gEAAM,QAAQ,MAAM;gEAAC;;;;;;;sEACtB,6LAAC;;gEAAM,KAAK,IAAI,CAAC,QAAQ,KAAK,CAAC,KAAK,MAAM,GAAG;gEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUpE;GAhLwB;;QAKP,qIAAA,CAAA,YAAS;;;KALF", "debugId": null}}]}