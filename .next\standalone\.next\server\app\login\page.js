(()=>{var e={};e.id=520,e.ids=[520],e.modules={363:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6378:(e,r,t)=>{Promise.resolve().then(t.bind(t,69488))},7968:(e,r,t)=>{Promise.resolve().then(t.bind(t,85578))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},18375:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,86346,23)),Promise.resolve().then(t.t.bind(t,27924,23)),Promise.resolve().then(t.t.bind(t,35656,23)),Promise.resolve().then(t.t.bind(t,40099,23)),Promise.resolve().then(t.t.bind(t,38243,23)),Promise.resolve().then(t.t.bind(t,28827,23)),Promise.resolve().then(t.t.bind(t,62763,23)),Promise.resolve().then(t.t.bind(t,97173,23))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21134:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},35247:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,16444,23)),Promise.resolve().then(t.t.bind(t,16042,23)),Promise.resolve().then(t.t.bind(t,88170,23)),Promise.resolve().then(t.t.bind(t,49477,23)),Promise.resolve().then(t.t.bind(t,29345,23)),Promise.resolve().then(t.t.bind(t,12089,23)),Promise.resolve().then(t.t.bind(t,46577,23)),Promise.resolve().then(t.t.bind(t,31307,23))},36328:(e,r,t)=>{"use strict";t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test2\\\\my-supabase-blog\\\\src\\\\components\\\\loading-spinner.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\loading-spinner.tsx","default")},39727:()=>{},43984:(e,r,t)=>{"use strict";t.d(r,{ThemeToggle:()=>l});var s=t(60687),n=t(43210),o=t(21134),i=t(363),a=t(10218);function l(){let{theme:e,setTheme:r}=(0,a.D)(),[t,l]=n.useState(!1);return(n.useEffect(()=>{l(!0)},[]),t)?(0,s.jsxs)("button",{onClick:()=>r("light"===e?"dark":"light"),className:"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 w-10",children:["light"===e?(0,s.jsx)(i.A,{className:"h-[1.2rem] w-[1.2rem]"}):(0,s.jsx)(o.A,{className:"h-[1.2rem] w-[1.2rem]"}),(0,s.jsx)("span",{className:"sr-only",children:"切換主題"})]}):(0,s.jsxs)("button",{className:"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 w-10",children:[(0,s.jsx)(o.A,{className:"h-[1.2rem] w-[1.2rem]"}),(0,s.jsx)("span",{className:"sr-only",children:"切換主題"})]})}},47990:()=>{},52331:(e,r,t)=>{Promise.resolve().then(t.bind(t,96871))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},61135:()=>{},62688:(e,r,t)=>{"use strict";t.d(r,{A:()=>u});var s=t(43210);let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,t)=>t?t.toUpperCase():r.toLowerCase()),i=e=>{let r=o(e);return r.charAt(0).toUpperCase()+r.slice(1)},a=(...e)=>e.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim(),l=e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,s.forwardRef)(({color:e="currentColor",size:r=24,strokeWidth:t=2,absoluteStrokeWidth:n,className:o="",children:i,iconNode:c,...u},m)=>(0,s.createElement)("svg",{ref:m,...d,width:r,height:r,stroke:e,strokeWidth:n?24*Number(t)/Number(r):t,className:a("lucide",o),...!i&&!l(u)&&{"aria-hidden":"true"},...u},[...c.map(([e,r])=>(0,s.createElement)(e,r)),...Array.isArray(i)?i:[i]])),u=(e,r)=>{let t=(0,s.forwardRef)(({className:t,...o},l)=>(0,s.createElement)(c,{ref:l,iconNode:r,className:a(`lucide-${n(i(e))}`,`lucide-${e}`,t),...o}));return t.displayName=i(e),t}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67393:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});var s=t(37413),n=t(36328);function o(){return(0,s.jsx)(n.default,{})}},69488:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>c});var s=t(60687),n=t(43210),o=t(79481),i=t(16189),a=t(85814),l=t.n(a),d=t(96044);function c(){let[e,r]=(0,n.useState)(""),[t,a]=(0,n.useState)(""),[c,u]=(0,n.useState)(!1),[m,p]=(0,n.useState)(""),[h,b]=(0,n.useState)(!1),x=(0,i.useRouter)(),f=(0,o.U)(),g=async r=>{r.preventDefault(),u(!0),p("");try{if(h){let{error:r}=await f.auth.signUp({email:e,password:t});if(r)throw r;p("Registration successful! Please check your email to verify your account.")}else{let{error:r}=await f.auth.signInWithPassword({email:e,password:t});if(r)throw r;x.push("/"),x.refresh()}}catch(e){p(e instanceof Error?e.message:"An error occurred")}finally{u(!1)}};return(0,s.jsxs)("div",{className:"min-h-screen bg-background",children:[(0,s.jsx)("header",{className:"bg-card/95 shadow-sm border-b border-border sticky top-0 z-50 backdrop-blur-sm",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 lg:py-6",children:(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)(l(),{href:"/",className:"text-xl sm:text-2xl font-bold text-foreground hover:text-primary transition-colors",children:"My Blog"}),(0,s.jsx)(d.ClientThemeToggle,{})]})})}),(0,s.jsx)("div",{className:"flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 mx-auto mb-6 bg-primary/10 rounded-full flex items-center justify-center",children:(0,s.jsx)("svg",{className:"w-8 h-8 text-primary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})})}),(0,s.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-foreground mb-2",children:h?"Create your account":"Welcome back"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:h?"Join our community today":"Sign in to your account"}),(0,s.jsxs)("p",{className:"mt-4 text-sm text-muted-foreground",children:["Or"," ",(0,s.jsx)(l(),{href:"/",className:"font-medium text-primary hover:text-primary/80 transition-colors",children:"return to home"})]})]}),(0,s.jsx)("div",{className:"bg-card rounded-xl shadow-lg border border-border p-6 sm:p-8",children:(0,s.jsxs)("form",{className:"space-y-6",onSubmit:g,children:[(0,s.jsxs)("div",{className:"space-y-5",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-foreground mb-2",children:"Email address"}),(0,s.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,className:"w-full px-4 py-3 border border-input bg-background text-foreground rounded-lg focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent transition-all duration-200 placeholder:text-muted-foreground",placeholder:"Enter your email address",value:e,onChange:e=>r(e.target.value)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-foreground mb-2",children:"Password"}),(0,s.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"current-password",required:!0,className:"w-full px-4 py-3 border border-input bg-background text-foreground rounded-lg focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent transition-all duration-200 placeholder:text-muted-foreground",placeholder:"Enter your password",value:t,onChange:e=>a(e.target.value)})]})]}),m&&(0,s.jsx)("div",{className:`text-sm text-center p-4 rounded-lg ${m.includes("successful")?"bg-green-50 text-green-700 border border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800":"bg-destructive/10 text-destructive border border-destructive/20"}`,children:m}),(0,s.jsx)("div",{children:(0,s.jsx)("button",{type:"submit",disabled:c,className:"w-full bg-primary text-primary-foreground py-3 px-4 rounded-lg hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-medium shadow-sm hover:shadow-md",children:c?(0,s.jsxs)("div",{className:"flex items-center justify-center",children:[(0,s.jsx)("div",{className:"w-5 h-5 border-2 border-current border-t-transparent rounded-full animate-spin mr-2"}),"Processing..."]}):h?"Create Account":"Sign In"})}),(0,s.jsx)("div",{className:"text-center",children:(0,s.jsx)("button",{type:"button",onClick:()=>b(!h),className:"text-primary hover:text-primary/80 text-sm transition-colors font-medium",suppressHydrationWarning:!0,children:h?"Already have an account? Sign in":"Don't have an account? Sign up"})})]})})]})})]})}},70440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n});var s=t(31658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},71874:(e,r,t)=>{Promise.resolve().then(t.bind(t,94934))},74075:e=>{"use strict";e.exports=require("zlib")},78592:(e,r,t)=>{Promise.resolve().then(t.bind(t,36328))},79428:e=>{"use strict";e.exports=require("buffer")},79481:(e,r,t)=>{"use strict";t.d(r,{U:()=>n});var s=t(59522);function n(){return(0,s.createBrowserClient)("https://sqoixvgmroejgaebxyeq.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.SFkF9v0Y-lrJuQHiSf5HLTLuwgOXRf0ERQKnfQNfLsU",{cookieOptions:{sameSite:"lax",secure:!0,path:"/"},auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0}})}},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83701:(e,r,t)=>{"use strict";t.d(r,{ThemeProvider:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\theme-provider.tsx","ThemeProvider")},85578:(e,r,t)=>{"use strict";t.d(r,{default:()=>n});var s=t(60687);function n(){return(0,s.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"h-24 w-24 rounded-full border-t-8 border-b-8 border-gray-200"}),(0,s.jsx)("div",{className:"absolute top-0 left-0 h-24 w-24 rounded-full border-t-8 border-b-8 border-blue-500 animate-spin"})]})})}t(43210)},91645:e=>{"use strict";e.exports=require("net")},92083:(e,r,t)=>{Promise.resolve().then(t.bind(t,83701))},94431:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>c,metadata:()=>d});var s=t(37413),n=t(22376),o=t.n(n),i=t(68726),a=t.n(i);t(61135);var l=t(83701);let d={title:"My Blog",description:"A modern blog built with Next.js and Supabase",icons:{icon:"/icon.png"}};function c({children:e}){return(0,s.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,s.jsxs)("body",{className:`${o().variable} ${a().variable} antialiased`,suppressHydrationWarning:!0,children:[(0,s.jsx)("meta",{name:"google-site-verification",content:"aY1T1sPS66KuuGAAe1zzVIZjS7YdM4MFbue__-3YBkI"}),(0,s.jsx)(l.ThemeProvider,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,suppressHydrationWarning:!0,children:e})]})})}},94735:e=>{"use strict";e.exports=require("events")},94934:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test2\\\\my-supabase-blog\\\\src\\\\app\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\login\\page.tsx","default")},96044:(e,r,t)=>{"use strict";t.d(r,{ClientThemeToggle:()=>o});var s=t(60687),n=t(43984);function o(){return(0,s.jsx)(n.ThemeToggle,{})}},96117:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=t(65239),n=t(48088),o=t(88170),i=t.n(o),a=t(30893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);t.d(r,l);let d={children:["",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,94934)),"C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\login\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,67393)),"C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\login\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},96871:(e,r,t)=>{"use strict";t.d(r,{ThemeProvider:()=>o});var s=t(60687);t(43210);var n=t(10218);function o({children:e,...r}){return(0,s.jsx)(n.N,{...r,children:e})}}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,98,567,318],()=>t(96117));module.exports=s})();